{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"typescript": "^4.9.0", "firebase-functions-test": "^3.1.0"}, "private": true}