<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useShowStorage } from '@/composables/useShowStorage'
import { useAuth } from '@/composables/useAuth'
import type { StoredShow, Song } from '@/types/'
import ShowDurationDisplay from '@/components/ShowDurationDisplay.vue'
import SetDurationDisplay from '@/components/SetDurationDisplay.vue'
import PDFSetList from '@/components/PDFSetList.vue'
import HTMLSetList from '@/components/HTMLSetList.vue'
import { format } from 'date-fns'
import { Cloud } from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()
const { loadShow, migrateToCloud } = useShowStorage()
const { user } = useAuth()

const show = ref<StoredShow | null>(null)
const isPDFPreviewOpen = ref(false)
const isHTMLPreviewOpen = ref(false)

onMounted(() => {
  const showId = route.params.id as string
  const loadedShow = loadShow(showId)
  if (loadedShow) {
    show.value = loadedShow
  } else {
    router.push('/')
  }
})

const handleEdit = () => {
  if (show.value) {
    router.push({ name: 'edit-show', params: { id: show.value.id } })
  }
}

const handleBack = () => {
  router.go(-1)
}

// Helper function for duration display
const getSongSets = (show: StoredShow): Song[][] => {
  return show.sets.map(set => set.songs)
}

const formatDate = (date: string) => {
  return format(new Date(date), 'dd MMM yyyy')
}

const getEncoreSongs = (songs: Song[]) => {
  const lastSongIndex = songs.findIndex(song => song.isLastSong)
  return lastSongIndex !== -1 ? songs.slice(lastSongIndex + 1) : []
}

const getMainSetSongs = (songs: Song[]) => {
  const lastSongIndex = songs.findIndex(song => song.isLastSong)
  return lastSongIndex !== -1 ? songs.slice(0, lastSongIndex + 1) : songs
}

const handleMigrateToCloud = async () => {
  if (!show.value || !user.value) return

  if (await migrateToCloud(show.value)) {
    // Update the local show data after successful migration
    show.value = {
      ...show.value,
      storageLocation: 'cloud',
      updatedAt: new Date().toISOString()
    }
  }
}
</script>

<template>
  <div v-if="show" class="show-view">
    <div class="show-header">
      <div class="show-header-left">
        <h1>{{ show.title ? show.title : 'Untitled Show' }}
          <BaseButton @click="handleMigrateToCloud" compact variant="ghost" class="migrate" title="Save to cloud">
            <Cloud :size="16" />
            Save to Cloud
          </BaseButton>
        </h1>
        <ShowDurationDisplay :sets="getSongSets(show)" />
      </div>
      <div class="actions">
        <BaseButton @click="handleEdit" class="button edit">Edit Show</BaseButton>
        <BaseButton @click="isPDFPreviewOpen = !isPDFPreviewOpen" class="button preview">
          {{ isPDFPreviewOpen ? 'Hide PDF' : 'Show PDF' }}
        </BaseButton>
        <BaseButton @click="isHTMLPreviewOpen = !isHTMLPreviewOpen" class="button preview">
          {{ isHTMLPreviewOpen ? 'Hide Print View' : 'Show Print View' }}
        </BaseButton>
      </div>
    </div>

    <div class="form-grid">
      <div class="form-group">
        <label>Venue</label>
        <div class="input readonly">{{ show.venue }}</div>
      </div>
      <div class="form-group">
        <label>Act</label>
        <div class="input readonly">{{ show.act }}</div>
      </div>
      <div class="form-group">
        <label>Date</label>
        <div class="input readonly">{{ formatDate(show.date) }}</div>
      </div>
    </div>

    <div class="sets-grid">
      <BaseCard v-for="(set, index) in show.sets" :key="set.id" class="set">
        <div class="set-header">
          <h2>Set {{ index + 1 }}</h2>
          <p>{{ set.name ? set.name : 'Untitled Set' }}</p>
          <SetDurationDisplay :set="set.songs" showCount />
        </div>
        <div class="songs">
          <div v-for="song in getMainSetSongs(set.songs)" :key="song.id" class="song">
            <span class="song-title">{{ song.title }}</span>
            <span class="song-key">{{ song.key }}</span>
          </div>

          <template v-if="getEncoreSongs(set.songs).length > 0">
            <div class="encore-divider">
              <span class="encore-label">Encore</span>
            </div>
            <div v-for="song in getEncoreSongs(set.songs)" :key="song.id" class="song encore">
              <span class="song-title">{{ song.title }}</span>
              <span class="song-key">{{ song.key }}</span>
            </div>
          </template>
        </div>
      </BaseCard>
    </div>

    <div class="actions bottom">
      <BaseButton @click="handleBack" class="cancel">Back to Shows</BaseButton>
    </div>

    <!-- PDF Preview Panel -->
    <Transition name="slide">
      <PDFSetList v-if="isPDFPreviewOpen" :gigData="show" @close="isPDFPreviewOpen = false" />
    </Transition>

    <!-- HTML Preview Panel -->
    <Transition name="slide">
      <HTMLSetList 
        v-if="isHTMLPreviewOpen" 
        :show="{ ...show, branding: '/branding.png' }" 
        @close="isHTMLPreviewOpen = false" 
      />
    </Transition>
  </div>
</template>

<style scoped>
.show-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-sm);
}

.show-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
  background-color: var(--color-surface);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  box-shadow: var(--bs-neu);
}

h1 {
  font-size: var(--font-size-2xl);
}

.actions {
  display: flex;
  gap: var(--space-xs);
  align-items: center;
}

.actions.bottom {
  justify-content: flex-end;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2xs);
}

.form-group label {
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
}

.input.readonly {
  background: var(--color-surface-muted);
  padding: var(--space-xs);
  border-radius: var(--radius-md);
  color: var(--color-text-light);
}

.sets-grid {
  display: grid;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.set {
  max-width: 600px;
  /* display: grid; */
  /* gap: var(--space-md); */
}

.set-header {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.songs {
  display: grid;
  gap: var(--space-xs);
  width: 100%;
}

.song {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-surface);
  border-radius: var(--radius-md);
  padding-inline: var(--space-xs);
}

.song:nth-child(even) {
  background: var(--color-surface-muted);
}

.song-key {
  font-family: var(--font-mono, monospace);
  color: var(--color-text-light);
}

/* Slide transition */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
}

.song.encore {
  background: var(--color-accent-light);
}

.song.encore:nth-child(even) {
  background: var(--color-accent-muted);
}

.encore-divider {
  margin: 0;
  text-align: center;
  position: relative;
}

.encore-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 2px;
  background: var(--color-accent);
}

.encore-label {
  background: var(--color-background);
  color: var(--color-accent);
  padding: 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  position: relative;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: 1;
  margin-block: 0;
}

.migrate {
  background: var(--color-accent);
  color: var(--color-text-white);
}

.migrate:hover {
  opacity: 0.9;
}
</style>